# Multi-stage Docker build for Spring Boot application

# Stage 1: Build the application
FROM openjdk:17-jdk-slim as builder

# Set working directory
WORKDIR /app

# Copy Gradle wrapper and build files
COPY gradlew .
COPY gradle gradle
COPY build.gradle .

# Copy source code
COPY src src

# Make gradlew executable
RUN chmod +x ./gradlew

# Build the application (this will also generate jOOQ code)
RUN ./gradlew clean build -x test

# Stage 2: Create the runtime image
FROM openjdk:17-jre-slim

# Set working directory
WORKDIR /app

# Create directory for H2 database files
RUN mkdir -p /app/data

# Copy the built JAR from the builder stage
COPY --from=builder /app/build/libs/*.jar app.jar

# Expose the application port
EXPOSE 8080

# Set environment variables
ENV SPRING_PROFILES_ACTIVE=prod
ENV JAVA_OPTS="-Xmx512m -Xms256m"

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# Run the application
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
