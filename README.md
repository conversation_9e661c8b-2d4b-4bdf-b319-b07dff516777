# 🔐 Spring Boot Authentication Service

A complete Spring Boot authentication service with JWT, H2 Database, jOOQ, and Swagger integration.

## 📋 Features

- ✅ **User Registration & Authentication**
- ✅ **JWT Token-based Security**
- ✅ **H2 Database** (In-memory & File-based)
- ✅ **jOOQ** for type-safe SQL queries
- ✅ **Swagger/OpenAPI 3** documentation
- ✅ **Docker** containerization
- ✅ **Profile-based Configuration**
- ✅ **Comprehensive Error Handling**
- ✅ **Input Validation**

## 🛠️ Tech Stack

- **Spring Boot 3.2.0**
- **Java 17+**
- **jOOQ** (Type-safe SQL)
- **H2 Database**
- **JWT (JSON Web Tokens)**
- **Swagger/OpenAPI 3**
- **Gradle 8.5**
- **Docker**
- **Lombok**
- **JUnit & Mockito**

## 📁 Project Structure

```
src/
├── main/
│   ├── java/com/example/auth/
│   │   ├── AuthApplication.java          # Main application class
│   │   ├── config/                       # Configuration classes
│   │   │   ├── SecurityConfig.java       # Spring Security configuration
│   │   │   └── SwaggerConfig.java        # Swagger/OpenAPI configuration
│   │   ├── controller/                   # REST controllers
│   │   │   └── AuthController.java       # Authentication endpoints
│   │   ├── service/                      # Business logic
│   │   │   ├── AuthService.java          # Authentication service
│   │   │   └── UserService.java          # User management service
│   │   ├── repository/                   # Data access layer
│   │   │   └── UserRepository.java       # jOOQ-based repository
│   │   ├── model/                        # Data models
│   │   │   ├── User.java                 # User entity
│   │   │   └── dto/                      # Data Transfer Objects
│   │   ├── security/                     # Security components
│   │   │   ├── JwtTokenProvider.java     # JWT token utilities
│   │   │   ├── JwtAuthenticationFilter.java
│   │   │   └── JwtAuthenticationEntryPoint.java
│   │   ├── exception/                    # Exception handling
│   │   │   └── GlobalExceptionHandler.java
│   │   └── jooq/                         # jOOQ generated code (auto-generated)
│   └── resources/
│       ├── application.properties        # Base configuration
│       ├── application-dev.properties    # Development profile
│       ├── application-prod.properties   # Production profile
│       └── schema.sql                    # Database schema
└── test/                                 # Unit tests
```

## 🚀 Quick Start

### Prerequisites

- **Java 17+**
- **Docker** (optional, for containerization)

### 1. Clone and Build

```bash
# Clone the repository
git clone <repository-url>
cd auth-service

# Build the project (this will generate jOOQ code)
./gradlew clean build
```

### 2. Run Locally

```bash
# Run with development profile (default)
./gradlew bootRun

# Or run the JAR directly
java -jar build/libs/auth-service-0.0.1-SNAPSHOT.jar
```

The application will start on `http://localhost:8080`

### 3. Run with Docker

```bash
# Build Docker image
docker build -t auth-service .

# Run container
docker run -p 8080:8080 auth-service
```

## 📚 API Documentation

### Swagger UI
Access the interactive API documentation at:
- **Local**: http://localhost:8080/swagger-ui.html
- **Docker**: http://localhost:8080/swagger-ui.html

### H2 Database Console
Access the H2 database console (development profile only):
- **URL**: http://localhost:8080/h2-console
- **JDBC URL**: `jdbc:h2:file:./data/authdb`
- **Username**: `sa`
- **Password**: (empty)

## 🔗 API Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/register` | Register new user | ❌ |
| POST | `/api/login` | User login | ❌ |
| GET | `/api/profile` | Get user profile | ✅ |
| POST | `/api/forgot-password` | Forgot password (mock) | ❌ |

### Example Requests

#### Register User
```bash
curl -X POST http://localhost:8080/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "John",
    "lastName": "Doe"
  }'
```

#### Login
```bash
curl -X POST http://localhost:8080/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### Get Profile (with JWT token)
```bash
curl -X GET http://localhost:8080/api/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## ⚙️ Configuration

### Profiles

- **dev** (default): Development with file-based H2, H2 console enabled
- **prod**: Production with file-based H2, H2 console disabled
- **test**: Testing with in-memory H2

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SPRING_PROFILES_ACTIVE` | Active Spring profile | `dev` |
| `JWT_SECRET` | JWT signing secret | `mySecretKey...` |
| `JWT_EXPIRATION` | JWT expiration time (ms) | `86400000` |

## 🗄️ Database

### Schema
The application uses H2 database with the following tables:
- **users**: User information
- **password_reset_tokens**: Password reset tokens (for future use)

### jOOQ Code Generation
jOOQ generates type-safe Java code from the database schema:

```bash
# Generate jOOQ code
./gradlew generateJooq
```

Generated code location: `src/main/java/com/example/auth/jooq/`

## 🧪 Testing

```bash
# Run all tests
./gradlew test

# Run tests with coverage
./gradlew test jacocoTestReport
```

## 🐳 Docker

### Build Image
```bash
docker build -t auth-service .
```

### Run Container
```bash
# Basic run
docker run -p 8080:8080 auth-service

# With environment variables
docker run -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e JWT_SECRET=your-secret-key \
  auth-service

# With volume for database persistence
docker run -p 8080:8080 \
  -v $(pwd)/data:/app/data \
  auth-service
```

## 🔧 Development

### Adding New Features

1. **Database Changes**: Update `schema.sql` and regenerate jOOQ code
2. **API Changes**: Update controllers and DTOs, Swagger will auto-update
3. **Security**: Modify `SecurityConfig.java` for new endpoints

### Code Generation

```bash
# Regenerate jOOQ code after schema changes
./gradlew generateJooq

# Clean and rebuild everything
./gradlew clean build
```

## 📝 Sample Data

The application includes sample users for testing:
- **Email**: `<EMAIL>`, **Password**: `password`
- **Email**: `<EMAIL>`, **Password**: `password`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

1. **jOOQ Generation Fails**: Ensure H2 database is accessible and schema.sql is valid
2. **JWT Errors**: Check JWT secret configuration
3. **Database Issues**: Verify H2 file permissions and paths
4. **Docker Build Fails**: Ensure Java 17+ is available in the container

### Logs

Check application logs for detailed error information:
```bash
# View logs in Docker
docker logs <container-id>

# Local development
tail -f logs/application.log
```
