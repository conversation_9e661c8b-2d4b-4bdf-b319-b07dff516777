@echo off
echo Setting up environment and testing Spring Boot Authentication Service...
echo.

REM Fix JAVA_HOME - remove \bin if it exists
set JAVA_HOME=C:\Program Files\Java\jdk-17
echo Using JAVA_HOME: %JAVA_HOME%
echo.

echo Step 1: Checking Java version...
java -version
if %ERRORLEVEL% neq 0 (
    echo Java not found! Please ensure Java 17+ is installed.
    pause
    exit /b 1
)
echo.

echo Step 2: Building the project...
call gradlew.bat clean build -x test
if %ERRORLEVEL% neq 0 (
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)
echo.

echo Step 3: Generating jOOQ code...
call gradlew.bat generateJooq
if %ERRORLEVEL% neq 0 (
    echo jOOQ generation failed! This is expected on first run.
    echo Let's try building again after jOOQ generation...
)
echo.

echo Step 4: Building again with jOOQ code...
call gradlew.bat build -x test
if %ERRORLEVEL% neq 0 (
    echo Second build failed! Please check the error messages above.
    pause
    exit /b 1
)
echo.

echo Step 5: Running tests...
call gradlew.bat test
if %ERRORLEVEL% neq 0 (
    echo Tests failed! Please check the error messages above.
    echo This might be expected if jOOQ code generation had issues.
)
echo.

echo Build completed successfully!
echo.
echo You can now:
echo 1. Run the application: gradlew.bat bootRun
echo 2. Access Swagger UI: http://localhost:8080/swagger-ui.html
echo 3. Access H2 Console: http://localhost:8080/h2-console
echo 4. Test with Docker: docker build -t auth-service . && docker run -p 8080:8080 auth-service
echo.
pause
