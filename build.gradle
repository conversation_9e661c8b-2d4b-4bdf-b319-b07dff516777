plugins {
    id 'org.springframework.boot' version '3.2.0'
    id 'io.spring.dependency-management' version '1.1.4'
    id 'java'
    id 'nu.studer.jooq' version '8.2'
}

group = 'com.example'
version = '0.0.1-SNAPSHOT'

java {
    sourceCompatibility = '17'
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-jooq'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    
    // Database
    runtimeOnly 'com.h2database:h2'
    jooqGenerator 'com.h2database:h2'
    
    // jOOQ
    implementation 'org.jooq:jooq'
    implementation 'org.jooq:jooq-meta'
    implementation 'org.jooq:jooq-codegen'
    
    // Swagger/OpenAPI
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0'
    
    // JWT
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'
    
    // Lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
}

// jOOQ Configuration
jooq {
    version = dependencyManagement.importedProperties['jooq.version']
    edition = nu.studer.gradle.jooq.JooqEdition.OSS
    
    configurations {
        main {
            generateSchemaSourceOnCompilation = true
            
            generationTool {
                logging = org.jooq.meta.jaxb.Logging.WARN
                jdbc {
                    driver = 'org.h2.Driver'
                    url = 'jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH'
                    user = 'sa'
                    password = ''
                }
                generator {
                    name = 'org.jooq.codegen.DefaultGenerator'
                    database {
                        name = 'org.jooq.meta.h2.H2Database'
                        inputSchema = 'PUBLIC'
                        includes = '.*'
                        excludes = ''
                    }
                    generate {
                        relations = true
                        deprecated = false
                        records = true
                        immutablePojos = false
                        fluentSetters = true
                        pojos = true
                        daos = true
                    }
                    target {
                        packageName = 'com.example.auth.jooq'
                        directory = 'src/main/java'
                    }
                }
            }
        }
    }
}

// Ensure jOOQ generation runs before compilation
compileJava.dependsOn generateJooq

// Configure Spring Boot
springBoot {
    mainClass = 'com.example.auth.AuthApplication'
}

tasks.named('test') {
    useJUnitPlatform()
}

// Task to initialize H2 database for jOOQ generation
task initDb(type: JavaExec) {
    classpath = configurations.jooqGenerator
    mainClass = 'org.h2.tools.RunScript'
    args = [
        '-url', 'jdbc:h2:mem:testdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH;INIT=CREATE SCHEMA IF NOT EXISTS PUBLIC',
        '-user', 'sa',
        '-password', '',
        '-script', 'src/main/resources/schema.sql'
    ]
}

generateJooq.dependsOn initDb
