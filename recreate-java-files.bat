@echo off
echo Creating Java source directory structure...

mkdir src\main\java\com\example\auth\config 2>nul
mkdir src\main\java\com\example\auth\controller 2>nul
mkdir src\main\java\com\example\auth\service 2>nul
mkdir src\main\java\com\example\auth\repository 2>nul
mkdir src\main\java\com\example\auth\model\dto 2>nul
mkdir src\main\java\com\example\auth\security 2>nul
mkdir src\main\java\com\example\auth\exception 2>nul
mkdir src\main\java\com\example\auth\jooq 2>nul

echo Directory structure created successfully!
echo.
echo You can now recreate the Java files using the save-file tool.
echo The following directories are ready:
echo - src\main\java\com\example\auth\config
echo - src\main\java\com\example\auth\controller  
echo - src\main\java\com\example\auth\service
echo - src\main\java\com\example\auth\repository
echo - src\main\java\com\example\auth\model\dto
echo - src\main\java\com\example\auth\security
echo - src\main\java\com\example\auth\exception
echo - src\main\java\com\example\auth\jooq
echo.
pause
