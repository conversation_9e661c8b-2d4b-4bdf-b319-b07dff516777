@echo off
echo Setting up Spring Boot Authentication Service...
echo.

echo Step 1: Building the project...
call gradlew.bat clean build
if %ERRORLEVEL% neq 0 (
    echo Build failed! Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Step 2: Generating jOOQ code...
call gradlew.bat generateJooq
if %ERRORLEVEL% neq 0 (
    echo jOOQ generation failed! Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Setup completed successfully!
echo.
echo You can now:
echo 1. Run the application: gradlew.bat bootRun
echo 2. Access Swagger UI: http://localhost:8080/swagger-ui.html
echo 3. Access H2 Console: http://localhost:8080/h2-console
echo.
pause
