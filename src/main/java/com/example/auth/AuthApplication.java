package com.example.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Main Spring Boot Application class for the Authentication Service.
 * 
 * This application provides:
 * - User registration and authentication
 * - JWT-based security
 * - H2 database with jOOQ integration
 * - Swagger API documentation
 * - Docker containerization support
 */
@SpringBootApplication
@EnableTransactionManagement
public class AuthApplication {

    public static void main(String[] args) {
        SpringApplication.run(AuthApplication.class, args);
    }
}
