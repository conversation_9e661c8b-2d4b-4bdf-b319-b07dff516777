package com.example.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Authentication controller for user registration and login.
 */
@RestController
@RequestMapping("/api")
@Tag(name = "Authentication", description = "User authentication endpoints")
public class AuthController {

    @PostMapping("/register")
    @Operation(summary = "Register a new user", description = "Register a new user with email and password")
    public Map<String, Object> register(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String password = request.get("password");
        String firstName = request.get("firstName");
        String lastName = request.get("lastName");
        
        // For testing purposes, return success response
        return Map.of(
            "success", true,
            "message", "User registered successfully",
            "email", email,
            "firstName", firstName,
            "lastName", lastName,
            "timestamp", LocalDateTime.now().toString(),
            "token", "test-jwt-token-" + System.currentTimeMillis()
        );
    }

    @PostMapping("/login")
    @Operation(summary = "User login", description = "Authenticate user with email and password")
    public Map<String, Object> login(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String password = request.get("password");
        
        // For testing purposes, return success response
        return Map.of(
            "success", true,
            "message", "Login successful",
            "email", email,
            "timestamp", LocalDateTime.now().toString(),
            "token", "test-jwt-token-" + System.currentTimeMillis()
        );
    }

    @PostMapping("/forgot-password")
    @Operation(summary = "Forgot password", description = "Request password reset")
    public Map<String, Object> forgotPassword(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        
        return Map.of(
            "success", true,
            "message", "Password reset email sent",
            "email", email,
            "timestamp", LocalDateTime.now().toString()
        );
    }

    @GetMapping("/profile")
    @Operation(summary = "Get user profile", description = "Get current user profile")
    public Map<String, Object> getProfile() {
        return Map.of(
            "success", true,
            "email", "<EMAIL>",
            "firstName", "Shashank",
            "lastName", "Admin",
            "timestamp", LocalDateTime.now().toString()
        );
    }
}
