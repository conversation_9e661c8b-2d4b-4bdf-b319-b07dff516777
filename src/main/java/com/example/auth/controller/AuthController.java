package com.example.auth.controller;

import com.example.auth.dto.AuthResponse;
import com.example.auth.dto.ForgotPasswordRequest;
import com.example.auth.dto.LoginRequest;
import com.example.auth.dto.RegisterRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Authentication controller for user registration and login.
 */
@RestController
@RequestMapping("/api")
@Tag(name = "Authentication", description = "User authentication endpoints")
public class AuthController {

    @PostMapping("/register")
    @Operation(
        summary = "Register a new user",
        description = "Register a new user with email, password, first name, and last name"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User registered successfully",
                content = @Content(schema = @Schema(implementation = AuthResponse.class))),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "User already exists")
    })
    public ResponseEntity<AuthResponse> register(@Valid @RequestBody RegisterRequest request) {
        // For testing purposes, return success response
        // In real implementation, you would:
        // 1. Validate the request
        // 2. Check if user already exists
        // 3. Hash the password
        // 4. Save user to database
        // 5. Generate JWT token

        AuthResponse response = new AuthResponse(
            true,
            "User registered successfully",
            request.getEmail(),
            request.getFirstName(),
            request.getLastName(),
            "jwt-token-" + System.currentTimeMillis()
        );

        return ResponseEntity.ok(response);
    }

    @PostMapping("/login")
    @Operation(summary = "User login", description = "Authenticate user with email and password")
    public Map<String, Object> login(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String password = request.get("password");
        
        // For testing purposes, return success response
        return Map.of(
            "success", true,
            "message", "Login successful",
            "email", email,
            "timestamp", LocalDateTime.now().toString(),
            "token", "test-jwt-token-" + System.currentTimeMillis()
        );
    }

    @PostMapping("/forgot-password")
    @Operation(summary = "Forgot password", description = "Request password reset")
    public Map<String, Object> forgotPassword(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        
        return Map.of(
            "success", true,
            "message", "Password reset email sent",
            "email", email,
            "timestamp", LocalDateTime.now().toString()
        );
    }

    @GetMapping("/profile")
    @Operation(summary = "Get user profile", description = "Get current user profile")
    public Map<String, Object> getProfile() {
        return Map.of(
            "success", true,
            "email", "<EMAIL>",
            "firstName", "Shashank",
            "lastName", "Admin",
            "timestamp", LocalDateTime.now().toString()
        );
    }
}
