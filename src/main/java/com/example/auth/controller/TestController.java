package com.example.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Simple test controller to verify the application works.
 */
@RestController
@RequestMapping("/api")
@Tag(name = "Test", description = "Test endpoints")
public class TestController {

    @GetMapping("/test")
    @Operation(summary = "Test endpoint", description = "Simple test to verify the application is running")
    public Map<String, String> test() {
        return Map.of(
            "status", "success",
            "message", "Spring Boot Authentication Service is running!",
            "timestamp", LocalDateTime.now().toString(),
            "version", "1.0.0"
        );
    }

    @GetMapping("/health")
    @Operation(summary = "Health check", description = "Application health status")
    public Map<String, String> health() {
        return Map.of(
            "status", "UP",
            "application", "auth-service",
            "timestamp", LocalDateTime.now().toString()
        );
    }
}
