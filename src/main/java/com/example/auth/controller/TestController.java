package com.example.auth.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Simple test controller to verify the application works.
 */
@RestController
@RequestMapping("/api")
public class TestController {

    @GetMapping("/test")
    public Map<String, String> test() {
        return Map.of(
            "status", "success",
            "message", "Spring Boot Authentication Service is running!",
            "timestamp", java.time.LocalDateTime.now().toString()
        );
    }
}
