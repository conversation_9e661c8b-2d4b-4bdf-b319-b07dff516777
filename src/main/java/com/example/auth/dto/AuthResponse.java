package com.example.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;

/**
 * Response DTO for authentication operations
 */
@Schema(description = "Authentication response")
public class AuthResponse {

    @Schema(description = "Operation success status", example = "true")
    private boolean success;

    @Schema(description = "Response message", example = "User registered successfully")
    private String message;

    @Schema(description = "User email", example = "<EMAIL>")
    private String email;

    @Schema(description = "User first name", example = "Shashank")
    private String firstName;

    @Schema(description = "User last name", example = "Admin")
    private String lastName;

    @Schema(description = "JWT authentication token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @Schema(description = "Response timestamp", example = "2025-06-13T13:02:13.783033")
    private LocalDateTime timestamp;

    // Default constructor
    public AuthResponse() {
        this.timestamp = LocalDateTime.now();
    }

    // Constructor for success response with token
    public AuthResponse(boolean success, String message, String email, String firstName, String lastName, String token) {
        this.success = success;
        this.message = message;
        this.email = email;
        this.firstName = firstName;
        this.lastName = lastName;
        this.token = token;
        this.timestamp = LocalDateTime.now();
    }

    // Constructor for simple success response
    public AuthResponse(boolean success, String message, String email) {
        this.success = success;
        this.message = message;
        this.email = email;
        this.timestamp = LocalDateTime.now();
    }

    // Getters and setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
}
