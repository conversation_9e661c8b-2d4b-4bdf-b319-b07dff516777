package com.example.auth.jooq;

import org.jooq.Field;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.time.LocalDateTime;

/**
 * This class is generated by jOOQ.
 */
public class PasswordResetTokensTable extends TableImpl<Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>PUBLIC.PASSWORD_RESET_TOKENS</code>
     */
    public static final PasswordResetTokensTable PASSWORD_RESET_TOKENS = new PasswordResetTokensTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<Record> getRecordType() {
        return Record.class;
    }

    /**
     * The column <code>PUBLIC.PASSWORD_RESET_TOKENS.ID</code>.
     */
    public final TableField<Record, Long> ID = createField(DSL.name("ID"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>PUBLIC.PASSWORD_RESET_TOKENS.TOKEN</code>.
     */
    public final TableField<Record, String> TOKEN = createField(DSL.name("TOKEN"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>PUBLIC.PASSWORD_RESET_TOKENS.USER_ID</code>.
     */
    public final TableField<Record, Long> USER_ID = createField(DSL.name("USER_ID"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>PUBLIC.PASSWORD_RESET_TOKENS.EXPIRES_AT</code>.
     */
    public final TableField<Record, LocalDateTime> EXPIRES_AT = createField(DSL.name("EXPIRES_AT"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>PUBLIC.PASSWORD_RESET_TOKENS.USED</code>.
     */
    public final TableField<Record, Boolean> USED = createField(DSL.name("USED"), SQLDataType.BOOLEAN.defaultValue(DSL.field("FALSE", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>PUBLIC.PASSWORD_RESET_TOKENS.CREATED_AT</code>.
     */
    public final TableField<Record, LocalDateTime> CREATED_AT = createField(DSL.name("CREATED_AT"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    private PasswordResetTokensTable(Name alias, Table<Record> aliased) {
        this(alias, aliased, null);
    }

    private PasswordResetTokensTable(Name alias, Table<Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), org.jooq.TableOptions.table());
    }

    /**
     * Create an aliased <code>PUBLIC.PASSWORD_RESET_TOKENS</code> table reference
     */
    public PasswordResetTokensTable(String alias) {
        this(DSL.name(alias), PASSWORD_RESET_TOKENS);
    }

    /**
     * Create an aliased <code>PUBLIC.PASSWORD_RESET_TOKENS</code> table reference
     */
    public PasswordResetTokensTable(Name alias) {
        this(alias, PASSWORD_RESET_TOKENS);
    }

    /**
     * Create a <code>PUBLIC.PASSWORD_RESET_TOKENS</code> table reference
     */
    public PasswordResetTokensTable() {
        this(DSL.name("PASSWORD_RESET_TOKENS"), null);
    }

    public PasswordResetTokensTable(Table<?> child, org.jooq.ForeignKey<?, Record> key) {
        super(child, key, PASSWORD_RESET_TOKENS);
    }

    @Override
    public Schema getSchema() {
        return null;
    }

    @Override
    public PasswordResetTokensTable as(String alias) {
        return new PasswordResetTokensTable(DSL.name(alias), this);
    }

    @Override
    public PasswordResetTokensTable as(Name alias) {
        return new PasswordResetTokensTable(alias, this);
    }
}
