package com.example.auth.jooq;

import org.jooq.Table;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;

/**
 * Convenience access to all tables in the schema.
 * This is a simplified version for manual creation.
 */
public class Tables {

    /**
     * The table <code>PUBLIC.USERS</code>.
     */
    public static final UsersTable USERS = UsersTable.USERS;

    /**
     * The table <code>PUBLIC.PASSWORD_RESET_TOKENS</code>.
     */
    public static final PasswordResetTokensTable PASSWORD_RESET_TOKENS = PasswordResetTokensTable.PASSWORD_RESET_TOKENS;
}
