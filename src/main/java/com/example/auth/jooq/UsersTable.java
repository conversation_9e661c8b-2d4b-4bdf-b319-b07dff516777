package com.example.auth.jooq;

import org.jooq.Field;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.time.LocalDateTime;

/**
 * This class is generated by jOOQ.
 */
public class UsersTable extends TableImpl<Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>PUBLIC.USERS</code>
     */
    public static final UsersTable USERS = new UsersTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<Record> getRecordType() {
        return Record.class;
    }

    /**
     * The column <code>PUBLIC.users.id</code>.
     */
    public final TableField<Record, Long> ID = createField(DSL.name("id"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>PUBLIC.users.email</code>.
     */
    public final TableField<Record, String> EMAIL = createField(DSL.name("email"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>PUBLIC.users.password</code>.
     */
    public final TableField<Record, String> PASSWORD = createField(DSL.name("password"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>PUBLIC.users.first_name</code>.
     */
    public final TableField<Record, String> FIRST_NAME = createField(DSL.name("first_name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>PUBLIC.users.last_name</code>.
     */
    public final TableField<Record, String> LAST_NAME = createField(DSL.name("last_name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>PUBLIC.users.enabled</code>.
     */
    public final TableField<Record, Boolean> ENABLED = createField(DSL.name("enabled"), SQLDataType.BOOLEAN.defaultValue(DSL.field("TRUE", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>PUBLIC.users.created_at</code>.
     */
    public final TableField<Record, LocalDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>PUBLIC.users.updated_at</code>.
     */
    public final TableField<Record, LocalDateTime> UPDATED_AT = createField(DSL.name("updated_at"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    private UsersTable(Name alias, Table<Record> aliased) {
        this(alias, aliased, null);
    }

    private UsersTable(Name alias, Table<Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), org.jooq.TableOptions.table());
    }

    /**
     * Create an aliased <code>PUBLIC.USERS</code> table reference
     */
    public UsersTable(String alias) {
        this(DSL.name(alias), USERS);
    }

    /**
     * Create an aliased <code>PUBLIC.USERS</code> table reference
     */
    public UsersTable(Name alias) {
        this(alias, USERS);
    }

    /**
     * Create a <code>PUBLIC.users</code> table reference
     */
    public UsersTable() {
        this(DSL.name("users"), null);
    }

    public UsersTable(Table<?> child, org.jooq.ForeignKey<?, Record> key) {
        super(child, key, USERS);
    }

    @Override
    public Schema getSchema() {
        return null;
    }

    @Override
    public UsersTable as(String alias) {
        return new UsersTable(DSL.name(alias), this);
    }

    @Override
    public UsersTable as(Name alias) {
        return new UsersTable(alias, this);
    }
}
