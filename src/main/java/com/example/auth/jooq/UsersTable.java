package com.example.auth.jooq;

import org.jooq.Field;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;

import java.time.LocalDateTime;

/**
 * This class is generated by jOOQ.
 */
public class UsersTable extends TableImpl<Record> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>PUBLIC.USERS</code>
     */
    public static final UsersTable USERS = new UsersTable();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<Record> getRecordType() {
        return Record.class;
    }

    /**
     * The column <code>PUBLIC.USERS.ID</code>.
     */
    public final TableField<Record, Long> ID = createField(DSL.name("ID"), SQLDataType.BIGINT.nullable(false), this, "");

    /**
     * The column <code>PUBLIC.USERS.EMAIL</code>.
     */
    public final TableField<Record, String> EMAIL = createField(DSL.name("EMAIL"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>PUBLIC.USERS.PASSWORD</code>.
     */
    public final TableField<Record, String> PASSWORD = createField(DSL.name("PASSWORD"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>PUBLIC.USERS.FIRST_NAME</code>.
     */
    public final TableField<Record, String> FIRST_NAME = createField(DSL.name("FIRST_NAME"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>PUBLIC.USERS.LAST_NAME</code>.
     */
    public final TableField<Record, String> LAST_NAME = createField(DSL.name("LAST_NAME"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>PUBLIC.USERS.ENABLED</code>.
     */
    public final TableField<Record, Boolean> ENABLED = createField(DSL.name("ENABLED"), SQLDataType.BOOLEAN.defaultValue(DSL.field("TRUE", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>PUBLIC.USERS.CREATED_AT</code>.
     */
    public final TableField<Record, LocalDateTime> CREATED_AT = createField(DSL.name("CREATED_AT"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>PUBLIC.USERS.UPDATED_AT</code>.
     */
    public final TableField<Record, LocalDateTime> UPDATED_AT = createField(DSL.name("UPDATED_AT"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    private UsersTable(Name alias, Table<Record> aliased) {
        this(alias, aliased, null);
    }

    private UsersTable(Name alias, Table<Record> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), org.jooq.TableOptions.table());
    }

    /**
     * Create an aliased <code>PUBLIC.USERS</code> table reference
     */
    public UsersTable(String alias) {
        this(DSL.name(alias), USERS);
    }

    /**
     * Create an aliased <code>PUBLIC.USERS</code> table reference
     */
    public UsersTable(Name alias) {
        this(alias, USERS);
    }

    /**
     * Create a <code>PUBLIC.USERS</code> table reference
     */
    public UsersTable() {
        this(DSL.name("USERS"), null);
    }

    public UsersTable(Table<?> child, org.jooq.ForeignKey<?, Record> key) {
        super(child, key, USERS);
    }

    @Override
    public Schema getSchema() {
        return null;
    }

    @Override
    public UsersTable as(String alias) {
        return new UsersTable(DSL.name(alias), this);
    }

    @Override
    public UsersTable as(Name alias) {
        return new UsersTable(alias, this);
    }
}
