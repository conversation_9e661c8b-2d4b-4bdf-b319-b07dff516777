package com.example.auth.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response DTO for authentication operations.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Authentication response")
public class AuthResponse {
    
    @Schema(description = "JWT access token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;
    
    @Schema(description = "Token type", example = "Bearer")
    private String type;
    
    @Schema(description = "User's email", example = "<EMAIL>")
    private String email;
    
    @Schema(description = "User's full name", example = "John Doe")
    private String fullName;
    
    @Schema(description = "Response message", example = "Login successful")
    private String message;
    
    public static AuthResponse success(String token, String email, String fullName, String message) {
        return AuthResponse.builder()
                .token(token)
                .type("Bearer")
                .email(email)
                .fullName(fullName)
                .message(message)
                .build();
    }
}
