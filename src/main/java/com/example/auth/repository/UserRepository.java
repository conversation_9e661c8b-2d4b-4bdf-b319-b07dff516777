package com.example.auth.repository;

import com.example.auth.model.User;
import lombok.RequiredArgsConstructor;
import org.jooq.DSLContext;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

import static com.example.auth.jooq.Tables.USERS;

/**
 * Repository for User entity using jOOQ for database operations.
 */
@Repository
@RequiredArgsConstructor
public class UserRepository {

    private final DSLContext dsl;

    /**
     * Find user by email.
     */
    public Optional<User> findByEmail(String email) {
        return dsl.selectFrom(USERS)
                .where(USERS.EMAIL.eq(email))
                .fetchOptional()
                .map(record -> User.builder()
                        .id(record.get(USERS.ID))
                        .email(record.get(USERS.EMAIL))
                        .password(record.get(USERS.PASSWORD))
                        .firstName(record.get(USERS.FIRST_NAME))
                        .lastName(record.get(USERS.LAST_NAME))
                        .enabled(record.get(USERS.ENABLED))
                        .createdAt(record.get(USERS.CREATED_AT))
                        .updatedAt(record.get(USERS.UPDATED_AT))
                        .build());
    }

    /**
     * Find user by ID.
     */
    public Optional<User> findById(Long id) {
        return dsl.selectFrom(USERS)
                .where(USERS.ID.eq(id))
                .fetchOptional()
                .map(record -> User.builder()
                        .id(record.get(USERS.ID))
                        .email(record.get(USERS.EMAIL))
                        .password(record.get(USERS.PASSWORD))
                        .firstName(record.get(USERS.FIRST_NAME))
                        .lastName(record.get(USERS.LAST_NAME))
                        .enabled(record.get(USERS.ENABLED))
                        .createdAt(record.get(USERS.CREATED_AT))
                        .updatedAt(record.get(USERS.UPDATED_AT))
                        .build());
    }

    /**
     * Check if user exists by email.
     */
    public boolean existsByEmail(String email) {
        return dsl.fetchExists(
                dsl.selectFrom(USERS)
                        .where(USERS.EMAIL.eq(email))
        );
    }

    /**
     * Save a new user.
     */
    public User save(User user) {
        LocalDateTime now = LocalDateTime.now();
        
        var record = dsl.insertInto(USERS)
                .set(USERS.EMAIL, user.getEmail())
                .set(USERS.PASSWORD, user.getPassword())
                .set(USERS.FIRST_NAME, user.getFirstName())
                .set(USERS.LAST_NAME, user.getLastName())
                .set(USERS.ENABLED, user.getEnabled() != null ? user.getEnabled() : true)
                .set(USERS.CREATED_AT, now)
                .set(USERS.UPDATED_AT, now)
                .returning()
                .fetchOne();

        return User.builder()
                .id(record.get(USERS.ID))
                .email(record.get(USERS.EMAIL))
                .password(record.get(USERS.PASSWORD))
                .firstName(record.get(USERS.FIRST_NAME))
                .lastName(record.get(USERS.LAST_NAME))
                .enabled(record.get(USERS.ENABLED))
                .createdAt(record.get(USERS.CREATED_AT))
                .updatedAt(record.get(USERS.UPDATED_AT))
                .build();
    }
}
