package com.example.auth.service;

import com.example.auth.model.User;
import com.example.auth.model.dto.*;
import com.example.auth.security.JwtTokenProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service class for authentication-related operations.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final UserService userService;
    private final JwtTokenProvider jwtTokenProvider;

    /**
     * Register a new user.
     */
    public AuthResponse register(RegisterRequest request) {
        log.info("Processing registration request for email: {}", request.getEmail());

        User user = userService.registerUser(
                request.getEmail(),
                request.getPassword(),
                request.getFirstName(),
                request.getLastName()
        );

        String token = jwtTokenProvider.generateToken(user.getEmail());

        return AuthResponse.success(
                token,
                user.getEmail(),
                user.getFullName(),
                "User registered successfully"
        );
    }

    /**
     * Authenticate user and generate JWT token.
     */
    public AuthResponse login(LoginRequest request) {
        log.info("Processing login request for email: {}", request.getEmail());

        User user = userService.authenticateUser(request.getEmail(), request.getPassword());
        String token = jwtTokenProvider.generateToken(user.getEmail());

        return AuthResponse.success(
                token,
                user.getEmail(),
                user.getFullName(),
                "Login successful"
        );
    }

    /**
     * Get user profile information.
     */
    public UserProfileResponse getProfile(String email) {
        log.info("Fetching profile for email: {}", email);

        User user = userService.getUserByEmail(email);

        return UserProfileResponse.builder()
                .id(user.getId())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .fullName(user.getFullName())
                .enabled(user.getEnabled())
                .createdAt(user.getCreatedAt())
                .build();
    }

    /**
     * Process forgot password request (mock implementation).
     */
    public ApiResponse forgotPassword(ForgotPasswordRequest request) {
        log.info("Processing forgot password request for email: {}", request.getEmail());

        // Check if user exists
        if (!userService.existsByEmail(request.getEmail())) {
            // For security reasons, we don't reveal if the email exists or not
            log.warn("Forgot password request for non-existent email: {}", request.getEmail());
        } else {
            log.info("Forgot password request for existing email: {}", request.getEmail());
            // In a real implementation, you would:
            // 1. Generate a password reset token
            // 2. Save it to the database with expiration
            // 3. Send an email with the reset link
        }

        return ApiResponse.success(
                "If the email exists in our system, you will receive a password reset link shortly."
        );
    }
}
