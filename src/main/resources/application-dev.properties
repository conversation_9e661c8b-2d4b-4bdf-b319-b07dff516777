# Development Profile Configuration

# H2 Database Configuration (File-based for persistence)
spring.datasource.url=jdbc:h2:file:./data/authdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2 Console Configuration (Enabled for development)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.h2.console.settings.web-allow-others=true

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Initialize database with schema.sql
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql

# jOOQ Configuration
spring.jooq.sql-dialect=H2

# Development specific settings
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.security=DEBUG
