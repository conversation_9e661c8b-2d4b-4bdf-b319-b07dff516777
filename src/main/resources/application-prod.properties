# Production Profile Configuration

# H2 Database Configuration (File-based for persistence)
spring.datasource.url=jdbc:h2:file:/app/data/authdb;MODE=PostgreSQL;DATABASE_TO_LOWER=TRUE;DEFAULT_NULL_ORDERING=HIGH
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2 Console Configuration (Disabled for production)
spring.h2.console.enabled=false

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false

# Initialize database with schema.sql
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql

# jOOQ Configuration
spring.jooq.sql-dialect=H2

# Production specific settings
logging.level.com.example.auth=INFO
logging.level.org.jooq=WARN
logging.level.org.springframework=WARN
