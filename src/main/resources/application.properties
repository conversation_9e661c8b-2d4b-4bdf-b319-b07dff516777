# Base Application Configuration
spring.application.name=auth-service

# Active Profile (default to dev for local development)
spring.profiles.active=dev

# Server Configuration
server.port=8080
server.servlet.context-path=/

# Logging Configuration
logging.level.com.example.auth=DEBUG
logging.level.org.jooq=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# JWT Configuration
jwt.secret=mySecretKey123456789012345678901234567890
jwt.expiration=86400000

# Swagger Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method

# Actuator Configuration
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=when-authorized
